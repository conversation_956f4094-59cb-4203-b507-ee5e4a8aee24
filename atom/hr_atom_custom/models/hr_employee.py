# -*- coding: utf-8 -*-

from odoo import models, fields, api

class HrEmployee(models.Model):
    _inherit = 'hr.employee'
    
    headquarters_id = fields.Many2one(
        'res.partner',
        string='Headquarters',
        domain="[('is_headquarters', '=', True)]",
        help='Select the headquarters where this employee is based'
    )
    
    mobility_ids = fields.Many2many(
        'res.country', 
        string='Mobility/Can Travel',
        help='Countries where the employee can travel to'
    )
    
    @api.depends('department_id', 'department_id.parent_id', 'department_id.master_department_id')
    def _compute_trade_domain(self):
        for employee in self:
            domain = []
            if employee.department_id:
                # Build domain conditions dynamically, only including valid department IDs
                conditions = []

                # Current department
                conditions.append(('department_id', '=', employee.department_id.id))

                # Parent department (if exists)
                if employee.department_id.parent_id:
                    conditions.append(('department_id', '=', employee.department_id.parent_id.id))

                # Master department (if exists)
                if employee.department_id.master_department_id:
                    conditions.append(('department_id', '=', employee.department_id.master_department_id.id))

                # Build domain with proper OR operators
                if len(conditions) == 1:
                    domain = conditions
                elif len(conditions) == 2:
                    domain = ['|'] + conditions
                elif len(conditions) == 3:
                    domain = ['|', '|'] + conditions

            employee.trade_domain = str(domain)

    trade_domain = fields.Char(
        compute='_compute_trade_domain',
        string='Available Trades Domain',
        store=False
    )

    trade_id = fields.Many2one(
        'hr.employee.trade',
        string='Trade'
    )
    
    # Replace float fields with many2one relationships
    self_reported_level = fields.Many2one(
        'hr.employee.level', 
        string='Self reported Level'
    )
    self_reported_level_calculated = fields.Many2one(
        'hr.employee.level',
        string='Self Reported Level (Skill Calculated)',
        compute='_compute_skill_levels',
        store=True,
        readonly=True
    )
    
    internal_level = fields.Many2one(
        'hr.employee.level',
        string='Internal Level'
    )
    internal_level_calculated = fields.Many2one(
        'hr.employee.level',
        string='Internal Level (Skill Calculated)',
        compute='_compute_skill_levels',
        store=True,
        readonly=True
    )
    
    years_of_experience = fields.Integer(
        string='Years of Experience',
        help='Manually filled'
    )
    years_of_experience_calculated = fields.Float(
        string='Years of Experience Calculated',
        help='Automatically calculated based on resume',
        readonly=True,
        compute='_compute_years_of_experience_calculated',
        # store=True # We don't need to store this field due to it is always calculated
    )
    
    @api.depends('skill_ids')
    def _compute_skill_levels(self):
        """
        Calculate self reported and internal levels based on employee skills.
        This is a placeholder implementation - you'll need to implement
        the actual calculation logic based on your requirements.
        """
        Level = self.env['hr.employee.level']
        for employee in self:
            # Placeholder calculation logic - implement as needed
            calculated_self_value = 0.0  # Replace with actual calculation
            calculated_internal_value = 0.0  # Replace with actual calculation
            
            # Find the levels with closest numeric values
            employee.self_reported_level_calculated = Level.search([
                ('numeric_value', '<=', calculated_self_value)
            ], order='numeric_value desc', limit=1)
            
            employee.internal_level_calculated = Level.search([
                ('numeric_value', '<=', calculated_internal_value)
            ], order='numeric_value desc', limit=1)
    
    @api.onchange('department_id')
    def _onchange_department_id(self):
        """Clear trade when department changes as they are related"""
        if self.department_id:
            self.trade_id = False

        # Return domain for trade_id field
        domain = []
        if self.department_id:
            # Build domain conditions dynamically, only including valid department IDs
            conditions = []

            # Current department
            conditions.append(('department_id', '=', self.department_id.id))

            # Parent department (if exists)
            if self.department_id.parent_id:
                conditions.append(('department_id', '=', self.department_id.parent_id.id))

            # Master department (if exists)
            if self.department_id.master_department_id:
                conditions.append(('department_id', '=', self.department_id.master_department_id.id))

            # Build domain with proper OR operators
            if len(conditions) == 1:
                domain = conditions
            elif len(conditions) == 2:
                domain = ['|'] + conditions
            elif len(conditions) == 3:
                domain = ['|', '|'] + conditions

        return {
            'domain': {
                'trade_id': domain
            }
        }

    def _compute_years_of_experience_calculated(self):
        for employee in self:
            employee.years_of_experience_calculated = 0.0